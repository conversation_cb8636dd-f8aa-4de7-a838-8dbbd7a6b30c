{"buildFiles": ["D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfig.cmake", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfigVersion.cmake", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\react-native-worklets\\react-native-workletsConfig.cmake", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\react-native-worklets\\react-native-workletsConfigVersion.cmake", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\16g1nt73\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"reanimated::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "reanimated", "output": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\16g1nt73\\obj\\armeabi-v7a\\libreanimated.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}