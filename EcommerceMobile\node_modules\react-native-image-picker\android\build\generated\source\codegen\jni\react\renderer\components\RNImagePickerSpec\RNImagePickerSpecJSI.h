/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeImagePickerCxxSpecJSI : public TurboModule {
protected:
  NativeImagePickerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void launchCamera(jsi::Runtime &rt, jsi::Object options, jsi::Function callback) = 0;
  virtual void launchImageLibrary(jsi::Runtime &rt, jsi::Object options, jsi::Function callback) = 0;

};

template <typename T>
class JSI_EXPORT NativeImagePickerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ImagePicker";

protected:
  NativeImagePickerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeImagePickerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeImagePickerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeImagePickerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void launchCamera(jsi::Runtime &rt, jsi::Object options, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::launchCamera) == 3,
          "Expected launchCamera(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::launchCamera, jsInvoker_, instance_, std::move(options), std::move(callback));
    }
    void launchImageLibrary(jsi::Runtime &rt, jsi::Object options, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::launchImageLibrary) == 3,
          "Expected launchImageLibrary(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::launchImageLibrary, jsInvoker_, instance_, std::move(options), std::move(callback));
    }

  private:
    friend class NativeImagePickerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
