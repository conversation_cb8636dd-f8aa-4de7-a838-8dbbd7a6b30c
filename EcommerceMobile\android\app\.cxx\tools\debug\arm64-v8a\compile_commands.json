[{"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/build/generated/autolinking/src/main/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\4e61384cd6e1721784fb2955c475fe8b\\EcommerceMobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/build/generated/autolinking/src/main/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\29c6ac0a1dd6ebb9204efeee5b088e58\\rnasyncstorageJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnstripe_autolinked_build\\CMakeFiles\\react_codegen_rnstripe.dir\\react\\renderer\\components\\rnstripe\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnstripe_autolinked_build\\CMakeFiles\\react_codegen_rnstripe.dir\\react\\renderer\\components\\rnstripe\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnstripe_autolinked_build\\CMakeFiles\\react_codegen_rnstripe.dir\\react\\renderer\\components\\rnstripe\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnstripe_autolinked_build\\CMakeFiles\\react_codegen_rnstripe.dir\\react\\renderer\\components\\rnstripe\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnstripe_autolinked_build\\CMakeFiles\\react_codegen_rnstripe.dir\\react\\renderer\\components\\rnstripe\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnstripe_autolinked_build\\CMakeFiles\\react_codegen_rnstripe.dir\\react\\renderer\\components\\rnstripe\\rnstripeJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\rnstripeJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnstripe\\rnstripeJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/@stripe/stripe-react-native/android/build/generated/source/codegen/jni/react/renderer/components/rnstripe\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnstripe_autolinked_build\\CMakeFiles\\react_codegen_rnstripe.dir\\rnstripe-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\rnstripe-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\@stripe\\stripe-react-native\\android\\build\\generated\\source\\codegen\\jni\\rnstripe-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\RNImagePickerSpec-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\b67162b4a9e608cf7419840e3175142c\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f53af68aeb87e0824508efe60ef39bfd\\RNCSafeAreaViewShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f53af68aeb87e0824508efe60ef39bfd\\RNCSafeAreaViewState.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7bcf54908a3eaa5938d705e9623f467d\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7d87002aa3ec9e921615745fbfad7289\\safeareacontext\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7d87002aa3ec9e921615745fbfad7289\\safeareacontext\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7d87002aa3ec9e921615745fbfad7289\\safeareacontext\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7d87002aa3ec9e921615745fbfad7289\\safeareacontext\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7bcf54908a3eaa5938d705e9623f467d\\safeareacontextJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\0a0e613ddf755dc3ed0dd34176cd916f\\jni\\safeareacontext-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a9232cb14562b303fbefb58739152f33\\rnscreens\\RNSBottomTabsShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a265e65047ad3fec2c4940a98397864a\\components\\rnscreens\\RNSBottomTabsState.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a9232cb14562b303fbefb58739152f33\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a9232cb14562b303fbefb58739152f33\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a265e65047ad3fec2c4940a98397864a\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\96969d318dff6186c0ef804436e2a701\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a9232cb14562b303fbefb58739152f33\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\96969d318dff6186c0ef804436e2a701\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a9232cb14562b303fbefb58739152f33\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a265e65047ad3fec2c4940a98397864a\\components\\rnscreens\\RNSScreenState.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a9232cb14562b303fbefb58739152f33\\rnscreens\\RNSSplitViewScreenShadowNode.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\41c90885ad1f416ce8eb01e7edb8716a\\components\\rnscreens\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ed275ce9589f0b09bc80f62a4c4ab015\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\14815cc815c78ec1aa5e368265ba3a45\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ed275ce9589f0b09bc80f62a4c4ab015\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\14815cc815c78ec1aa5e368265ba3a45\\react\\renderer\\components\\rnscreens\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\41c90885ad1f416ce8eb01e7edb8716a\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\796ad459e5c56493b6ccb0d610a9a963\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\68c0b361f082d70d15ec3e1cd2e973b0\\RNCWebViewSpecJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\Props.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\States.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp"}, {"directory": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/android/app/.cxx/Debug/47111i50/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\rnworklets-generated.cpp.o -c \"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp\"", "file": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp"}]