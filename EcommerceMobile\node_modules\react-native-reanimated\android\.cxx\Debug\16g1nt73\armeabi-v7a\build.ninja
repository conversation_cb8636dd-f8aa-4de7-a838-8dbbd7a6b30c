# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Reanimated
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target reanimated


#############################################
# Order-only phony target for reanimated

build cmake_object_order_depends_target_reanimated: phony || CMakeFiles/reanimated.dir

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\AnimatedSensor\AnimatedSensorModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\AnimatedSensor
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/Quaternion.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/Quaternion.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common\Quaternion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/TransformMatrix.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/TransformMatrix.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common\TransformMatrix.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common\values\CSSAngle.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSBoolean.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSBoolean.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values\CSSBoolean.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common\values\CSSColor.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSDimension.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSDimension.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values\CSSDimension.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/common/values/CSSDiscreteArray.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSDiscreteArray.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\common\values\CSSDiscreteArray.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSKeyword.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSKeyword.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values\CSSKeyword.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSNumber.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSNumber.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values\CSSNumber.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\common\values
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/vectors.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/vectors.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common\vectors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\common
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/config/CSSAnimationConfig.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/CSSAnimationConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\config\CSSAnimationConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\config
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/config/CSSKeyframesConfig.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/CSSKeyframesConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\config\CSSKeyframesConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\config
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/config/CSSTransitionConfig.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/CSSTransitionConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\config\CSSTransitionConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\config
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/config/common.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/common.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\config\common.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\config
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\core\CSSAnimation.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\core
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/core/CSSTransition.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/core/CSSTransition.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\core\CSSTransition.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\core
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing\EasingFunctions.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing\cubicBezier.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/linear.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/linear.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing\linear.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/steps.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/steps.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing\steps.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\easing
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/interpolation/InterpolatorFactory.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/InterpolatorFactory.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\interpolation\InterpolatorFactory.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\interpolation
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/interpolation/PropertyInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/PropertyInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\interpolation\PropertyInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\interpolation
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/d47789b1a785e75381cee3d90be1a7b1/interpolation/groups/ArrayPropertiesInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/ArrayPropertiesInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\d47789b1a785e75381cee3d90be1a7b1\interpolation\groups\ArrayPropertiesInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\d47789b1a785e75381cee3d90be1a7b1\interpolation\groups
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/d47789b1a785e75381cee3d90be1a7b1/interpolation/groups/GroupPropertiesInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/GroupPropertiesInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\d47789b1a785e75381cee3d90be1a7b1\interpolation\groups\GroupPropertiesInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\d47789b1a785e75381cee3d90be1a7b1\interpolation\groups
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/df6d24d68c314b6b24e0ac8f510b0bbd/groups/RecordPropertiesInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/RecordPropertiesInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\df6d24d68c314b6b24e0ac8f510b0bbd\groups\RecordPropertiesInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\df6d24d68c314b6b24e0ac8f510b0bbd\groups
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/d47789b1a785e75381cee3d90be1a7b1/interpolation/styles/TransitionStyleInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/styles/TransitionStyleInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\d47789b1a785e75381cee3d90be1a7b1\interpolation\styles\TransitionStyleInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\d47789b1a785e75381cee3d90be1a7b1\interpolation\styles
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/interpolation/transforms/TransformOperation.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperation.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\interpolation\transforms\TransformOperation.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\interpolation\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/df6d24d68c314b6b24e0ac8f510b0bbd/transforms/TransformOperationInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperationInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\df6d24d68c314b6b24e0ac8f510b0bbd\transforms\TransformOperationInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\df6d24d68c314b6b24e0ac8f510b0bbd\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/df6d24d68c314b6b24e0ac8f510b0bbd/transforms/TransformsStyleInterpolator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformsStyleInterpolator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\df6d24d68c314b6b24e0ac8f510b0bbd\transforms\TransformsStyleInterpolator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\df6d24d68c314b6b24e0ac8f510b0bbd\transforms
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\misc\ViewStylesRepository.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\misc
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/progress/AnimationProgressProvider.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/AnimationProgressProvider.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\progress\AnimationProgressProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\progress
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/progress/RawProgressProvider.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/RawProgressProvider.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\progress\RawProgressProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\progress
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/progress/TransitionProgressProvider.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/TransitionProgressProvider.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\progress\TransitionProgressProvider.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\CSS\progress
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/registry/CSSAnimationsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/CSSAnimationsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\registry\CSSAnimationsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\registry
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/registry/CSSKeyframesRegistry.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/CSSKeyframesRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\registry\CSSKeyframesRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\registry
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/registry/CSSTransitionsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/CSSTransitionsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\registry\CSSTransitionsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\CSS\registry
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/registry/StaticPropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/StaticPropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\registry\StaticPropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\registry
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/util/DelayedItemsManager.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/DelayedItemsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\util\DelayedItemsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\CSS\util
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/algorithms.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/algorithms.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util\algorithms.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/interpolators.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/interpolators.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util\interpolators.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/keyframes.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/keyframes.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util\keyframes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/props.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/props.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util\props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\CSS\util
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\Fabric\ReanimatedCommitHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Fabric\ReanimatedMountHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Fabric\ShadowTreeCloner.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\Fabric\updates\AnimatedPropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\Fabric\updates
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\Fabric\updates\UpdatesRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\Fabric\updates
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/Fabric/updates/UpdatesRegistryManager.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/UpdatesRegistryManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\Fabric\updates\UpdatesRegistryManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\Fabric\updates
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/LayoutAnimations/LayoutAnimationsManager.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\LayoutAnimations\LayoutAnimationsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/LayoutAnimations/LayoutAnimationsProxy.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\LayoutAnimations\LayoutAnimationsProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/LayoutAnimations/LayoutAnimationsUtils.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\LayoutAnimations\LayoutAnimationsUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\LayoutAnimations
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/NativeModules/PropValueProcessor.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/PropValueProcessor.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\NativeModules\PropValueProcessor.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\f3b80677aa8ab5daaee379062b1e2660\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\NativeModules\ReanimatedModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/NativeModules/ReanimatedModuleProxySpec.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\NativeModules\ReanimatedModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\8240ad715c66394312a8bfc66042d410\NativeModules
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\RuntimeDecorators\RNRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\RuntimeDecorators\UIRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\6d9b00adbb44c87364eb32ffde2ecd54\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Tools/FeatureFlags.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeatureFlags.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Tools\FeatureFlags.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Tools\ReanimatedVersion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\52de5d955f6ef415f7a832d7d659d112\Common\cpp\reanimated\Tools
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/NativeProxy.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android\NativeProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb

build CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/OnLoad.cpp.o: CXX_COMPILER__reanimated_Debug D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor" -I"D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets" -isystem "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = CMakeFiles\reanimated.dir\src\main\cpp\reanimated\android
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target reanimated


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.so

build ../../../../build/intermediates/cxx/Debug/16g1nt73/obj/armeabi-v7a/libreanimated.so: CXX_SHARED_LIBRARY_LINKER__reanimated_Debug CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/Quaternion.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/TransformMatrix.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSBoolean.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSDimension.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/common/values/CSSDiscreteArray.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSKeyword.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/common/values/CSSNumber.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/common/vectors.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/config/CSSAnimationConfig.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/config/CSSKeyframesConfig.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/config/CSSTransitionConfig.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/config/common.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/core/CSSTransition.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/linear.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/easing/steps.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/interpolation/InterpolatorFactory.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/interpolation/PropertyInterpolator.cpp.o CMakeFiles/reanimated.dir/d47789b1a785e75381cee3d90be1a7b1/interpolation/groups/ArrayPropertiesInterpolator.cpp.o CMakeFiles/reanimated.dir/d47789b1a785e75381cee3d90be1a7b1/interpolation/groups/GroupPropertiesInterpolator.cpp.o CMakeFiles/reanimated.dir/df6d24d68c314b6b24e0ac8f510b0bbd/groups/RecordPropertiesInterpolator.cpp.o CMakeFiles/reanimated.dir/d47789b1a785e75381cee3d90be1a7b1/interpolation/styles/TransitionStyleInterpolator.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/interpolation/transforms/TransformOperation.cpp.o CMakeFiles/reanimated.dir/df6d24d68c314b6b24e0ac8f510b0bbd/transforms/TransformOperationInterpolator.cpp.o CMakeFiles/reanimated.dir/df6d24d68c314b6b24e0ac8f510b0bbd/transforms/TransformsStyleInterpolator.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/progress/AnimationProgressProvider.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/progress/RawProgressProvider.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/CSS/progress/TransitionProgressProvider.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/registry/CSSAnimationsRegistry.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/registry/CSSKeyframesRegistry.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/CSS/registry/CSSTransitionsRegistry.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/registry/StaticPropsRegistry.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/CSS/util/DelayedItemsManager.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/algorithms.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/interpolators.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/keyframes.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/CSS/util/props.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/Fabric/updates/UpdatesRegistryManager.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/LayoutAnimations/LayoutAnimationsManager.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/LayoutAnimations/LayoutAnimationsProxy.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/LayoutAnimations/LayoutAnimationsUtils.cpp.o CMakeFiles/reanimated.dir/f3b80677aa8ab5daaee379062b1e2660/cpp/reanimated/NativeModules/PropValueProcessor.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o CMakeFiles/reanimated.dir/8240ad715c66394312a8bfc66042d410/NativeModules/ReanimatedModuleProxySpec.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o CMakeFiles/reanimated.dir/6d9b00adbb44c87364eb32ffde2ecd54/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Tools/FeatureFlags.cpp.o CMakeFiles/reanimated.dir/52de5d955f6ef415f7a832d7d659d112/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/NativeProxy.cpp.o CMakeFiles/reanimated.dir/src/main/cpp/reanimated/android/OnLoad.cpp.o | C$:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/intermediates/cxx/Debug/3g17334e/obj/armeabi-v7a/libworklets.so C$:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS="[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so"  -landroid  "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/intermediates/cxx/Debug/3g17334e/obj/armeabi-v7a/libworklets.so"  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = CMakeFiles\reanimated.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreanimated.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\reanimated.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\16g1nt73\obj\armeabi-v7a\libreanimated.pdb
  RSP_FILE = CMakeFiles\reanimated.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android\.cxx\Debug\16g1nt73\armeabi-v7a" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android\.cxx\Debug\16g1nt73\armeabi-v7a" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android" -B"D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android\.cxx\Debug\16g1nt73\armeabi-v7a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libreanimated.so: phony ../../../../build/intermediates/cxx/Debug/16g1nt73/obj/armeabi-v7a/libreanimated.so

build reanimated: phony ../../../../build/intermediates/cxx/Debug/16g1nt73/obj/armeabi-v7a/libreanimated.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a

build all: phony ../../../../build/intermediates/cxx/Debug/16g1nt73/obj/armeabi-v7a/libreanimated.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a/CMakeFiles/cmake.verify_globs | ../../../../CMakeLists.txt ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/react-native-worklets/react-native-workletsConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/react-native-worklets/react-native-workletsConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/react-native-worklets/react-native-workletsConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/react-native-worklets/react-native-workletsConfigVersion.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake D$:/Dong_A/Nam$ III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
