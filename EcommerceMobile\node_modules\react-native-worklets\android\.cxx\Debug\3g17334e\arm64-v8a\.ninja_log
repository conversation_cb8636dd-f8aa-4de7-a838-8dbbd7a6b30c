# ninja log v5
7	52	0	D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/.cxx/Debug/3g17334e/arm64-v8a/CMakeFiles/cmake.verify_globs	95bff33d9dabdf92
73	225	7750513845436061	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Resources/ValueUnpacker.cpp.o	c5aab6cdb93f0f8b
105	10140	7750513944768598	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/FeatureFlags.cpp.o	891e9ff5bd0caefc
58	13097	7750513973852181	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o	d732d9a7421c49cf
43	14528	7750513987797624	CMakeFiles/worklets.dir/5fd2d7ce45fba8b8e973d7ee5f4c19e4/worklets/AnimationFrameQueue/AnimationFrameBatchinator.cpp.o	f45dd51889f227e4
90	15143	7750513994795722	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	385eb26476aa2d6b
190	15280	7750513995143833	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o	33ec01553242f509
156	15303	7750513995958822	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o	8a5e59c133158cae
172	15399	7750513996702867	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSScheduler.cpp.o	537f9ec3a45085e5
208	15542	7750513998365855	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/UIScheduler.cpp.o	234b4675f41b5a2c
137	15798	7750514000999774	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSLogger.cpp.o	d85931648d8847d0
122	18114	7750514024039248	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o	bb885276ca276988
15799	19905	7750514042487350	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/PlatformLogger.cpp.o	7082d5af96288398
15144	22220	7750514064631891	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/UIRuntimeDecorator.cpp.o	580fe91b81aba79d
15280	25552	7750514097522808	CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/WorkletHermesRuntime.cpp.o	ad56bfae5e1c0ca1
13100	26488	7750514107805779	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o	a631c9056fd6acd8
226	26627	7750514109393392	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o	a16d506d029787c7
19905	28374	7750514127059518	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/AndroidUIScheduler.cpp.o	11226d49673a220b
17	28500	7750514128246265	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o	55f4440bb805d649
10151	33825	7750514181055465	CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o	e90ac6edd70af31f
15543	34021	7750514183561698	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsOnLoad.cpp.o	5c7e4ca31a446f5e
30	34902	7750514192316051	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o	97946b62385af316
14529	35101	7750514194397554	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/RuntimeManager.cpp.o	a5893f91f8094b20
18115	35972	7750514203019611	CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsModule.cpp.o	965e672e6886244f
15304	36747	7750514210668168	CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o	628ed690bc229bcb
15445	36757	7750514210447676	CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	150ecd10609f84fd
4	38745	7750514230634949	CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o	99dac8ac6baa0ecd
