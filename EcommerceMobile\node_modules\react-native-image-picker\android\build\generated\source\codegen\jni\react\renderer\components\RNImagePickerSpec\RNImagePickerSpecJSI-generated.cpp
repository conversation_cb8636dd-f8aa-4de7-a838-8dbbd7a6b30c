/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleCpp.js
 */

#include "RNImagePickerSpecJSI.h"

namespace facebook::react {

static jsi::Value __hostFunction_NativeImagePickerCxxSpecJSI_launchCamera(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImagePickerCxxSpecJSI *>(&turboModule)->launchCamera(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImagePickerCxxSpecJSI_launchImageLibrary(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImagePickerCxxSpecJSI *>(&turboModule)->launchImageLibrary(
    rt,
    count <= 0 ? throw jsi::JSError(rt, "Expected argument in position 0 to be passed") : args[0].asObject(rt),
    count <= 1 ? throw jsi::JSError(rt, "Expected argument in position 1 to be passed") : args[1].asObject(rt).asFunction(rt)
  );
  return jsi::Value::undefined();
}

NativeImagePickerCxxSpecJSI::NativeImagePickerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImagePicker", jsInvoker) {
  methodMap_["launchCamera"] = MethodMetadata {2, __hostFunction_NativeImagePickerCxxSpecJSI_launchCamera};
  methodMap_["launchImageLibrary"] = MethodMetadata {2, __hostFunction_NativeImagePickerCxxSpecJSI_launchImageLibrary};
}


} // namespace facebook::react
