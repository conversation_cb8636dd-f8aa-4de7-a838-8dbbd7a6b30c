
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "RNImagePickerSpec.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeImagePickerSpecJSI_launchCamera(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "launchCamera", "(Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeImagePickerSpecJSI_launchImageLibrary(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "launchImageLibrary", "(Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

NativeImagePickerSpecJSI::NativeImagePickerSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["launchCamera"] = MethodMetadata {2, __hostFunction_NativeImagePickerSpecJSI_launchCamera};
  methodMap_["launchImageLibrary"] = MethodMetadata {2, __hostFunction_NativeImagePickerSpecJSI_launchImageLibrary};
}

std::shared_ptr<TurboModule> RNImagePickerSpec_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "ImagePicker") {
    return std::make_shared<NativeImagePickerSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
