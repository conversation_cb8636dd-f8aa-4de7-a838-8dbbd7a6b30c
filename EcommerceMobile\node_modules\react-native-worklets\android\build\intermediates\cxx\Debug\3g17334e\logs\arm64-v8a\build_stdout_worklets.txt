ninja: Entering directory `D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-worklets\android\.cxx\Debug\3g17334e\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Resources/ValueUnpacker.cpp.o
[2/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/FeatureFlags.cpp.o
[3/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o
[4/27] Building CXX object CMakeFiles/worklets.dir/5fd2d7ce45fba8b8e973d7ee5f4c19e4/worklets/AnimationFrameQueue/AnimationFrameBatchinator.cpp.o
[5/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/AsyncQueue.cpp.o
[6/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o
[7/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o
[8/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSScheduler.cpp.o
[9/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/UIScheduler.cpp.o
[10/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSLogger.cpp.o
[11/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o
[12/27] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/PlatformLogger.cpp.o
[13/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/UIRuntimeDecorator.cpp.o
[14/27] Building CXX object CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/WorkletHermesRuntime.cpp.o
[15/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o
[16/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o
[17/27] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/AndroidUIScheduler.cpp.o
[18/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o
[19/27] Building CXX object CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o
[20/27] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsOnLoad.cpp.o
[21/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o
[22/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/RuntimeManager.cpp.o
[23/27] Building CXX object CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsModule.cpp.o
[24/27] Building CXX object CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o
[25/27] Building CXX object CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o
[26/27] Building CXX object CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o
[27/27] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\3g17334e\obj\arm64-v8a\libworklets.so
FAILED: ../../../../build/intermediates/cxx/Debug/3g17334e/obj/arm64-v8a/libworklets.so 
cmd.exe /C "cd . && C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\271~1.122\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot="C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -fPIC -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80         -DWORKLETS_VERSION=0.4.0         -DWORKLETS_FEATURE_FLAGS="[IOS_DYNAMIC_FRAMERATE_ENABLED:true]" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -DJS_RUNTIME_HERMES=1 -DHERMES_ENABLE_DEBUGGER=1 -fno-limit-debug-info  -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -shared -Wl,-soname,libworklets.so -o ..\..\..\..\build\intermediates\cxx\Debug\3g17334e\obj\arm64-v8a\libworklets.so CMakeFiles/worklets.dir/5fd2d7ce45fba8b8e973d7ee5f4c19e4/worklets/AnimationFrameQueue/AnimationFrameBatchinator.cpp.o CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Resources/ValueUnpacker.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/AsyncQueue.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/FeatureFlags.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSLogger.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSScheduler.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/UIScheduler.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/RuntimeManager.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/UIRuntimeDecorator.cpp.o CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/WorkletHermesRuntime.cpp.o CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp.o CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o CMakeFiles/worklets.dir/src/main/cpp/worklets/android/AndroidUIScheduler.cpp.o CMakeFiles/worklets.dir/src/main/cpp/worklets/android/PlatformLogger.cpp.o CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsModule.cpp.o CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsOnLoad.cpp.o  -llog  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/816951bc71efb4c99db481b15113489d/transformed/hermes-android-0.80.1-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so"  "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/hermestooling/libs/android.arm64-v8a/libhermestooling.so"  -latomic -lm && cd ."
ld.lld: error: undefined symbol: std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_string(std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&)
>>> referenced by JSIWorkletsModuleProxy.cpp:119 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp:119)
>>>               CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o:(worklets::JSIWorkletsModuleProxy::JSIWorkletsModuleProxy(bool, std::__ndk1::shared_ptr<facebook::react::BigStringBuffer const> const&, std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, std::__ndk1::shared_ptr<facebook::react::MessageQueueThread> const&, std::__ndk1::shared_ptr<worklets::JSScheduler> const&, std::__ndk1::shared_ptr<worklets::UIScheduler> const&, std::__ndk1::shared_ptr<worklets::RuntimeManager> const&, std::__ndk1::weak_ptr<worklets::WorkletRuntime> const&))
>>> referenced by JSIWorkletsModuleProxy.cpp:131 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp:131)
>>>               CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o:(worklets::JSIWorkletsModuleProxy::JSIWorkletsModuleProxy(worklets::JSIWorkletsModuleProxy const&))
>>> referenced by WorkletsModuleProxy.cpp:41 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp:41)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o:(worklets::WorkletsModuleProxy::WorkletsModuleProxy(facebook::jsi::Runtime&, std::__ndk1::shared_ptr<facebook::react::MessageQueueThread> const&, std::__ndk1::shared_ptr<facebook::react::CallInvoker> const&, std::__ndk1::shared_ptr<worklets::UIScheduler> const&, std::__ndk1::function<bool ()>&&, std::__ndk1::function<void (std::__ndk1::function<void (double)>)>&&, std::__ndk1::shared_ptr<facebook::react::BigStringBuffer const> const&, std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&))
>>> referenced 24 more times

ld.lld: error: undefined symbol: std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::~basic_string()
>>> referenced by JSISerializer.cpp:9 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/JSISerializer.cpp:9)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o:(__cxx_global_var_init)
>>> referenced by WorkletsVersion.cpp:29 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/WorkletsVersion.cpp:29)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(worklets::injectWorkletsCppVersion(facebook::jsi::Runtime&))
>>> referenced by WorkletsVersion.cpp:29 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/WorkletsVersion.cpp:29)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(worklets::injectWorkletsCppVersion(facebook::jsi::Runtime&))
>>> referenced 287 more times

ld.lld: error: undefined symbol: vtable for std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced 1 more times
>>> the vtable symbol may be undefined because the class is missing its key function (see https://lld.llvm.org/missingkeyfunction)

ld.lld: error: undefined symbol: VTT for std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced 3 more times

ld.lld: error: undefined symbol: std::__ndk1::cout
>>> referenced by WorkletsVersion.cpp:66 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/WorkletsVersion.cpp:66)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(worklets::checkJSVersion(facebook::jsi::Runtime&, std::__ndk1::shared_ptr<worklets::JSLogger> const&))
>>> referenced by WorkletsVersion.cpp:66 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/WorkletsVersion.cpp:66)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(worklets::checkJSVersion(facebook::jsi::Runtime&, std::__ndk1::shared_ptr<worklets::JSLogger> const&))

ld.lld: error: undefined symbol: std::__ndk1::mutex::~mutex()
>>> referenced by WorkletRuntimeRegistry.cpp:0 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp:0)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o:(__cxx_global_var_init.1)
>>> referenced by WorkletRuntimeRegistry.cpp:0 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp:0)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o:(__cxx_global_var_init.1)
>>> referenced by shared_mutex:166 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/shared_mutex:166)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o:(std::__ndk1::__shared_mutex_base::~__shared_mutex_base[abi:ne180000]())
>>> referenced 4 more times

ld.lld: error: undefined symbol: std::__ndk1::__shared_weak_count::~__shared_weak_count()
>>> referenced by shared_ptr.h:263 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:263)
>>>               CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o:(std::__ndk1::__shared_ptr_emplace<worklets::WorkletRuntimeCollector, std::__ndk1::allocator<worklets::WorkletRuntimeCollector>>::__shared_ptr_emplace[abi:ne180000]<facebook::jsi::Runtime&, std::__ndk1::allocator<worklets::WorkletRuntimeCollector>, 0>(std::__ndk1::allocator<worklets::WorkletRuntimeCollector>, facebook::jsi::Runtime&))
>>> referenced by shared_ptr.h:246 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:246)
>>>               CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o:(std::__ndk1::__shared_ptr_emplace<worklets::WorkletRuntimeCollector, std::__ndk1::allocator<worklets::WorkletRuntimeCollector>>::~__shared_ptr_emplace())
>>> referenced by shared_ptr.h:263 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:263)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o:(std::__ndk1::__shared_ptr_emplace<worklets::JSScheduler, std::__ndk1::allocator<worklets::JSScheduler>>::__shared_ptr_emplace[abi:ne180000]<facebook::jsi::Runtime&, std::__ndk1::shared_ptr<facebook::react::CallInvoker> const&, std::__ndk1::function<bool ()>, std::__ndk1::allocator<worklets::JSScheduler>, 0>(std::__ndk1::allocator<worklets::JSScheduler>, facebook::jsi::Runtime&, std::__ndk1::shared_ptr<facebook::react::CallInvoker> const&, std::__ndk1::function<bool ()>&&))
>>> referenced 86 more times

ld.lld: error: undefined symbol: std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>::operator<<(bool)
>>> referenced by WorkletsVersion.cpp:66 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/WorkletsVersion.cpp:66)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(worklets::checkJSVersion(facebook::jsi::Runtime&, std::__ndk1::shared_ptr<worklets::JSLogger> const&))

ld.lld: error: undefined symbol: __cxa_allocate_exception
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by References-inl.h:120 (C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include/fbjni/detail/References-inl.h:120)
>>>               CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsModule.cpp.o:(std::__ndk1::enable_if<IsNonWeakReference<facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*>>(), facebook::jni::reference_traits<facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*>>::plain_jni_reference_t>::type facebook::jni::detail::make_ref<facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*>, facebook::jni::GlobalReferenceAllocator>(facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*> const&))
>>> referenced by Shareables.cpp:249 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:249)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::extractShareableOrThrow(facebook::jsi::Runtime&, facebook::jsi::Value const&, std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&))
>>> referenced 28 more times

ld.lld: error: undefined symbol: std::runtime_error::runtime_error(char const*)
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by Shareables.cpp:249 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:249)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::extractShareableOrThrow(facebook::jsi::Runtime&, facebook::jsi::Value const&, std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&))
>>> referenced by JSISerializer.cpp:333 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/JSISerializer.cpp:333)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o:(worklets::JSISerializer::stringifyJSIValueRecursively(facebook::jsi::Value const&, bool))
>>> referenced 1 more times

ld.lld: error: undefined symbol: typeinfo for std::runtime_error
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by Shareables.cpp:249 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:249)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::extractShareableOrThrow(facebook::jsi::Runtime&, facebook::jsi::Value const&, std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&))
>>> referenced 13 more times

ld.lld: error: undefined symbol: std::runtime_error::~runtime_error()
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by JSISerializer.cpp:333 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/JSISerializer.cpp:333)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o:(worklets::JSISerializer::stringifyJSIValueRecursively(facebook::jsi::Value const&, bool))
>>> referenced 13 more times

ld.lld: error: undefined symbol: __cxa_throw
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by References-inl.h:120 (C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include/fbjni/detail/References-inl.h:120)
>>>               CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsModule.cpp.o:(std::__ndk1::enable_if<IsNonWeakReference<facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*>>(), facebook::jni::reference_traits<facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*>>::plain_jni_reference_t>::type facebook::jni::detail::make_ref<facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*>, facebook::jni::GlobalReferenceAllocator>(facebook::jni::alias_ref<facebook::jni::detail::JTypeFor<facebook::jni::HybridClass<worklets::WorkletsModule, facebook::jni::detail::BaseHybridClass>::JavaPart, facebook::jni::JObject, void>::_javaobject*> const&))
>>> referenced by JSISerializer.cpp:333 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/JSISerializer.cpp:333)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o:(worklets::JSISerializer::stringifyJSIValueRecursively(facebook::jsi::Value const&, bool))
>>> referenced 28 more times

ld.lld: error: undefined symbol: __cxa_free_exception
>>> referenced by Shareables.cpp:104 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/SharedItems/Shareables.cpp:104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/SharedItems/Shareables.cpp.o:(worklets::makeShareableClone(facebook::jsi::Runtime&, facebook::jsi::Value const&, facebook::jsi::Value const&, facebook::jsi::Value const&))
>>> referenced by JSISerializer.cpp:333 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/Tools/JSISerializer.cpp:333)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o:(worklets::JSISerializer::stringifyJSIValueRecursively(facebook::jsi::Value const&, bool))
>>> referenced by regex:989 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/regex:989)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(void std::__ndk1::__throw_regex_error[abi:ne180000]<(std::__ndk1::regex_constants::error_type)17>())
>>> referenced 23 more times

ld.lld: error: undefined symbol: std::__ndk1::basic_istream<char, std::__ndk1::char_traits<char>>::~basic_istream()
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced by sstream:1104 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:1104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::~basic_istringstream())
>>> referenced by istream:1173 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/istream:1173)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o:(std::__ndk1::basic_iostream<char, std::__ndk1::char_traits<char>>::basic_iostream[abi:ne180000](std::__ndk1::basic_streambuf<char, std::__ndk1::char_traits<char>>*))

ld.lld: error: undefined symbol: std::__ndk1::basic_ios<char, std::__ndk1::char_traits<char>>::~basic_ios()
>>> referenced by sstream:801 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:801)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_istringstream[abi:ne180000](std::__ndk1::basic_string<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>> const&, unsigned int))
>>> referenced by sstream:1104 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:1104)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::basic_istringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::~basic_istringstream())
>>> referenced by sstream:1010 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/sstream:1010)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/JSISerializer.cpp.o:(std::__ndk1::basic_stringstream<char, std::__ndk1::char_traits<char>, std::__ndk1::allocator<char>>::basic_stringstream[abi:ne180000]())
>>> referenced 1 more times

ld.lld: error: undefined symbol: std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>::put(char)
>>> referenced by ostream:890 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream:890)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>& std::__ndk1::endl[abi:ne180000]<char, std::__ndk1::char_traits<char>>(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&))

ld.lld: error: undefined symbol: std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>::flush()
>>> referenced by ostream:891 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/ostream:891)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsVersion.cpp.o:(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>& std::__ndk1::endl[abi:ne180000]<char, std::__ndk1::char_traits<char>>(std::__ndk1::basic_ostream<char, std::__ndk1::char_traits<char>>&))

ld.lld: error: undefined symbol: std::__ndk1::basic_istream<char, std::__ndk1::char_traits<char>>::operator>>(float&)
>>> referenced by istream_iterator.h:57 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/istream_iterator.h:57)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::istream_iterator<float, char, std::__ndk1::char_traits<char>, long>::istream_iterator[abi:ne180000](std::__ndk1::basic_istream<char, std::__ndk1::char_traits<char>>&))
>>> referenced by istream_iterator.h:64 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__iterator/istream_iterator.h:64)
>>>               CMakeFiles/worklets.dir/6b505363f4be3fa1b777327bed608aa6/Common/cpp/worklets/Tools/WorkletsJSIUtils.cpp.o:(std::__ndk1::istream_iterator<float, char, std::__ndk1::char_traits<char>, long>::operator++[abi:ne180000]())

ld.lld: error: undefined symbol: operator delete(void*)
>>> referenced by shared_ptr.h:246 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__memory/shared_ptr.h:246)
>>>               CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o:(std::__ndk1::__shared_ptr_emplace<worklets::WorkletRuntimeCollector, std::__ndk1::allocator<worklets::WorkletRuntimeCollector>>::~__shared_ptr_emplace())
>>> referenced by function.h:250 (C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/__functional/function.h:250)
>>>               CMakeFiles/worklets.dir/src/main/cpp/worklets/android/WorkletsOnLoad.cpp.o:(std::__ndk1::__function::__func<JNI_OnLoad::$_0, std::__ndk1::allocator<JNI_OnLoad::$_0>, void ()>::~__func())
>>> referenced by JSIWorkletsModuleProxy.cpp:138 (D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/Common/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp:138)
>>>               CMakeFiles/worklets.dir/28bb4e4875c391e1b429abd5ce6fc1e9/cpp/worklets/NativeModules/JSIWorkletsModuleProxy.cpp.o:(worklets::JSIWorkletsModuleProxy::~JSIWorkletsModuleProxy())
>>> referenced 207 more times

ld.lld: error: too many errors emitted, stopping now (use --error-limit=0 to see all errors)
CLANG_~1: error: linker command failed with exit code 1 (use -v to see invocation)
ninja: build stopped: subcommand failed.
