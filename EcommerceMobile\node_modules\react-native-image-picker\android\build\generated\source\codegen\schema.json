{"modules": {"NativeImagePicker": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "launchCamera", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "options", "optional": false, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}, {"name": "callback", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": []}}]}}, {"name": "launchImageLibrary", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "options", "optional": false, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}, {"name": "callback", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": []}}]}}]}, "moduleName": "ImagePicker"}}}