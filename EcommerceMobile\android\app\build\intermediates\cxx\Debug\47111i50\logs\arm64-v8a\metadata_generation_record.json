[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\.cxx\\Debug\\47111i50\\arm64-v8a\\android_gradle_build.json due to:", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Java\\\\jdk-23\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging16263138175953096041\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\1d221509c521a46e74f6572064742339\\\\transformed\\\\react-android-0.80.1-debug\\\\prefab\" ^\n  \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-reanimated\\\\524xf575\" ^\n  \"D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-worklets\\\\6n3sf5p4\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\816951bc71efb4c99db481b15113489d\\\\transformed\\\\hermes-android-0.80.1-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.14.1\\\\transforms\\\\b6a6ccfe82730aba80016e7d9940f54a\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\.cxx\\Debug\\47111i50\\arm64-v8a'", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\.cxx\\Debug\\47111i50\\arm64-v8a'", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\47111i50\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\47111i50\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\.cxx\\\\Debug\\\\47111i50\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BD:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\.cxx\\\\Debug\\\\47111i50\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\47111i50\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\47111i50\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\.cxx\\\\Debug\\\\47111i50\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BD:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\.cxx\\\\Debug\\\\47111i50\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\Dong_A\\\\Nam III\\\\KienTap\\\\Python-KienTap-\\\\EcommerceMobile\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\.cxx\\Debug\\47111i50\\arm64-v8a\\compile_commands.json.bin normally", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\.cxx\\Debug\\47111i50\\arm64-v8a\\compile_commands.json to D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\android\\app\\.cxx\\tools\\debug\\arm64-v8a\\compile_commands.json", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]