                        -HD:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android\build\intermediates\cxx\Debug\16g1nt73\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android\build\intermediates\cxx\Debug\16g1nt73\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=D:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android\.cxx\Debug\16g1nt73\prefab\x86\prefab
-BD:\Dong_A\Nam III\KienTap\Python-KienTap-\EcommerceMobile\node_modules\react-native-reanimated\android\.cxx\Debug\16g1nt73\x86
-GNinja
-DANDROID_STL=c++_shared
-DREACT_NATIVE_MINOR_VERSION=80
-DANDROID_TOOLCHAIN=clang
-DREACT_NATIVE_DIR=D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native
-DIS_REANIMATED_EXAMPLE_APP=false
-DREANIMATED_PROFILING=false
-DREANIMATED_VERSION=4.0.0
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
-DREANIMATED_FEATURE_FLAGS=[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]
                        Build command args: []
                        Version: 2