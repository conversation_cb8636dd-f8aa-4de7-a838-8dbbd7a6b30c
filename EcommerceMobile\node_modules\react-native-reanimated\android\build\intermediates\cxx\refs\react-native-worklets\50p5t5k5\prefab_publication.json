{"installationFolder": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-worklets", "packageInfo": {"packageName": "react-native-worklets", "packageVersion": "0.4.0", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "worklets", "moduleHeaders": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "x86", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\intermediates\\cxx\\Debug\\3g17334e\\obj\\x86\\libworklets.so", "abiAndroidGradleBuildJsonFile": "D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\.cxx\\Debug\\3g17334e\\x86\\android_gradle_build.json"}]}]}}