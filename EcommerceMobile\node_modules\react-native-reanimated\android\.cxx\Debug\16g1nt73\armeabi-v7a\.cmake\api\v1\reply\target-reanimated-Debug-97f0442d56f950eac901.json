{"artifacts": [{"path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/16g1nt73/obj/armeabi-v7a/libreanimated.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 54, "parent": 0}, {"command": 1, "file": 0, "line": 72, "parent": 0}, {"command": 1, "file": 0, "line": 74, "parent": 0}, {"command": 1, "file": 0, "line": 77, "parent": 0}, {"command": 2, "file": 0, "line": 12, "parent": 0}, {"command": 3, "file": 0, "line": 57, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=80    -DREANIMATED_PROFILING=false    -DREANIMATED_VERSION=4.0.0    -DREANIMATED_FEATURE_FLAGS=\"[DISABLE_COMMIT_PAUSING_MECHANISM:false][ANDROID_SYNCHRONOUSLY_UPDATE_UI_PROPS:false][EXAMPLE_STATIC_FLAG:true]\" -fexceptions -frtti -std=c++20 -Wall -Werror -fno-omit-frame-pointer -fstack-protector-all -fno-limit-debug-info  -fPIC"}, {"backtrace": 5, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 5, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 5, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 5, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 5, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}, {"fragment": "-std=gnu++20"}], "defines": [{"define": "reanimated_EXPORTS"}], "includes": [{"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/../Common/cpp"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/src/main/cpp"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/yoga"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/callinvoker"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/runtimeexecutor"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/jsiexecutor"}, {"backtrace": 6, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 3, "isSystem": true, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-worklets/android/build/prefab-headers/worklets"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "reanimated::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"backtrace": 2, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b6a6ccfe82730aba80016e7d9940f54a\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so\"", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 3, "fragment": "\"D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\intermediates\\cxx\\Debug\\3g17334e\\obj\\armeabi-v7a\\libworklets.so\"", "role": "libraries"}, {"backtrace": 4, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\1d221509c521a46e74f6572064742339\\transformed\\react-android-0.80.1-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so\"", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "reanimated", "nameOnDisk": "libreanimated.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/Quaternion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/TransformMatrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSAngle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSBoolean.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSColor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSDimension.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSDiscreteArray.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSKeyword.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/values/CSSNumber.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/common/vectors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/CSSAnimationConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/CSSKeyframesConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/CSSTransitionConfig.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/config/common.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/core/CSSAnimation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/core/CSSTransition.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/EasingFunctions.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/cubicBezier.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/linear.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/easing/steps.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/InterpolatorFactory.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/PropertyInterpolator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/ArrayPropertiesInterpolator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/GroupPropertiesInterpolator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/groups/RecordPropertiesInterpolator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/styles/TransitionStyleInterpolator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformOperationInterpolator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/interpolation/transforms/TransformsStyleInterpolator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/misc/ViewStylesRepository.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/AnimationProgressProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/RawProgressProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/progress/TransitionProgressProvider.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/CSSAnimationsRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/CSSKeyframesRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/CSSTransitionsRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/registry/StaticPropsRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/DelayedItemsManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/algorithms.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/interpolators.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/keyframes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/CSS/util/props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/AnimatedPropsRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/UpdatesRegistry.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/updates/UpdatesRegistryManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/PropValueProcessor.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeatureFlags.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/ReanimatedVersion.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/reanimated/android/NativeProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/reanimated/android/OnLoad.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}