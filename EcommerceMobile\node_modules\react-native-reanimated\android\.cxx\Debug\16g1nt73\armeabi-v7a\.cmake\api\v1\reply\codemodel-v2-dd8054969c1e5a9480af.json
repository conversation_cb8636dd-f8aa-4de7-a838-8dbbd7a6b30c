{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Reanimated", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "reanimated::@6890427a1f51a3e7e1df", "jsonFile": "target-reanimated-Debug-97f0442d56f950eac901.json", "name": "reanimated", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android/.cxx/Debug/16g1nt73/armeabi-v7a", "source": "D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native-reanimated/android"}, "version": {"major": 2, "minor": 3}}