
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "rnasyncstorage.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiGet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "multiGet", "(Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiSet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "multiSet", "(Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiRemove(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "multiRemove", "(Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_multiMerge(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "multiMerge", "(Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_getAllKeys(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "getAllKeys", "(Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeAsyncStorageModuleSpecJSI_clear(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "clear", "(Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

NativeAsyncStorageModuleSpecJSI::NativeAsyncStorageModuleSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["multiGet"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiGet};
  methodMap_["multiSet"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiSet};
  methodMap_["multiRemove"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiRemove};
  methodMap_["multiMerge"] = MethodMetadata {2, __hostFunction_NativeAsyncStorageModuleSpecJSI_multiMerge};
  methodMap_["getAllKeys"] = MethodMetadata {1, __hostFunction_NativeAsyncStorageModuleSpecJSI_getAllKeys};
  methodMap_["clear"] = MethodMetadata {1, __hostFunction_NativeAsyncStorageModuleSpecJSI_clear};
}

std::shared_ptr<TurboModule> rnasyncstorage_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "RNCAsyncStorage") {
    return std::make_shared<NativeAsyncStorageModuleSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
