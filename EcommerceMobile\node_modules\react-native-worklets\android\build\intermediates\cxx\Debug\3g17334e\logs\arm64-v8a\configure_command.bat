@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HD:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=arm64-v8a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\intermediates\\cxx\\Debug\\3g17334e\\obj\\arm64-v8a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\build\\intermediates\\cxx\\Debug\\3g17334e\\obj\\arm64-v8a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=D:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\.cxx\\Debug\\3g17334e\\prefab\\arm64-v8a\\prefab" ^
  "-BD:\\Dong_A\\Nam III\\KienTap\\Python-KienTap-\\EcommerceMobile\\node_modules\\react-native-worklets\\android\\.cxx\\Debug\\3g17334e\\arm64-v8a" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared" ^
  "-DREACT_NATIVE_MINOR_VERSION=80" ^
  "-DANDROID_TOOLCHAIN=clang" ^
  "-DREACT_NATIVE_DIR=D:/Dong_A/Nam III/KienTap/Python-KienTap-/EcommerceMobile/node_modules/react-native" ^
  "-DJS_RUNTIME=hermes" ^
  "-DIS_REANIMATED_EXAMPLE_APP=false" ^
  "-DWORKLETS_BUNDLE_MODE=false" ^
  "-DWORKLETS_VERSION=0.4.0" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON" ^
  "-DWORKLETS_FEATURE_FLAGS=[IOS_DYNAMIC_FRAMERATE_ENABLED:true]" ^
  "-DHERMES_ENABLE_DEBUGGER=1"
